package net.xianmu.usercenter.starter.config;

import com.alibaba.fastjson.JSON;
import net.xianmu.common.exception.CallerException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.dubbo.support.constant.DubboCommonConstant;
import net.xianmu.i18n.result.util.I18nCommonResultUtil;
import net.xianmu.i18n.result.util.I18nDubboResponseUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * 内部处理了controller层级dubbo的异常
 * 1.controller使用了2种方式,1.注解ControllerAdvice,2,实现HandlerExceptionResolver,注解方式会被优先匹配处理,没被注解匹配到的会走HandlerExceptionResolver
 * 2.dubbo处理,基于实现net.xianmu.dubbo.support.handle.ExceptionHandler 的接口方法
 */
@Component
@RestControllerAdvice
public class CustomerExceptionHandler implements net.xianmu.dubbo.support.handle.ExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomerExceptionHandler.class);


    /**
     * 异常书写顺序
     * 1.在用异常在前面
     * 2.调用方异常在前面
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public CommonResult providerException(Exception e) {
        CommonResult result = null;
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            String message = ex.getBindingResult().getAllErrors().stream()
            .map(DefaultMessageSourceResolvable::getDefaultMessage).findFirst().orElse("参数异常");
            result = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, message);
            logger.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
        } else if (e instanceof ParamsException) {
            ParamsException ex = (ParamsException) e;
            result = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, ex.getMessage(), ex.getErrorCode().getCode());
            logger.warn("调用方参数异常, 异常信息:{}", ex.getMessage(), ex);
        } else if (e instanceof CallerException) {
            CallerException ex = (CallerException) e;
            result = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, ex.getMessage(), ex.getErrorCode().getCode());
            logger.warn("调用方异常, 异常信息:{}", ex.getMessage(), ex);
        } else if (e instanceof net.xianmu.common.exception.BizException) {
            net.xianmu.common.exception.BizException ex = (net.xianmu.common.exception.BizException) e;
            result = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, ex.getMessage(), ex.getErrorCode().getCode());
            logger.warn("调用方异常, 异常信息:{}", ex.getMessage(), ex);
        } else if (e instanceof ProviderException) {
            ProviderException ex = (ProviderException) e;
            result = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, ex.getMessage(), ex.getErrorCode().getCode());
            logger.error("提供方异常, 异常信息:{}", ex.getMessage(), ex);
        } else if (e instanceof DuplicateKeyException) {
            String message = e.getCause().getMessage();
            result = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, message);
            logger.error("提供方异常,异常信息:{}", e.getMessage(), e);
        } else if (e instanceof DataIntegrityViolationException){
            result = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage());
            logger.error("提供方异常,异常信息:{}", e.getMessage(), e);
        } else {
            result = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage());
            logger.error("提供方未知异常,异常信息:{}", e.getMessage(), e);
        }

        logger.info("接口响应参数：responseBody = [{}]", JSON.toJSONString(result));

        return result;
    }
    
    @Override
    public DubboResponse processError(Throwable e, ProceedingJoinPoint joinPoint) {
        if (e instanceof ParamsException) {
            ParamsException exception = (ParamsException)e;
            logger.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else if (e instanceof net.xianmu.common.exception.BizException) {
            net.xianmu.common.exception.BizException exception = (net.xianmu.common.exception.BizException) e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else if (e instanceof CallerException) {
            CallerException exception = (CallerException)e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else if (e instanceof ProviderException) {
            ProviderException exception = (ProviderException)e;
            logger.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else {
            logger.error("提供方未知异常, 异常信息:{}", e.getMessage(), e);
            ProviderErrorCode providerErrorCode = new ProviderErrorCode(DubboCommonConstant.UNDEFINED_EXCEPTION_CODE);
            return I18nDubboResponseUtil.getError(providerErrorCode.getCode(), e.getMessage(), null);
        }
    }
}
