package net.xianmu.usercenter.starter.config;

import net.xianmu.authentication.shiro.filter.PermissionFilter;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/22 18:48
 */
@Configuration
public class ShiroConfigFilter {

    /**
     * ShiroFilter是整个Shiro的入口点，用于拦截需要安全控制的请求进行处理
     */
    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);
        shiroFilter.setLoginUrl("/summerfarm/home.html#/login");
        Map<String, String> filterMap = new LinkedHashMap<>();
        filterMap.put("/ok", "anon");
        filterMap.put("/**", "authc");
        shiroFilter.setFilterChainDefinitionMap(filterMap);

        Map<String, Filter> filterWonMap = new LinkedHashMap<>();
        filterWonMap.put("authc", new PermissionFilter());
        shiroFilter.setFilters(filterWonMap);
        return shiroFilter;
    }
}
