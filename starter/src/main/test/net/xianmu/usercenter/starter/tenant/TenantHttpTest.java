package net.xianmu.usercenter.starter.tenant;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.usercenter.inbound.tenant.controller.TenantController;
import net.xianmu.usercenter.inbound.tenant.vo.TenantPageQueryVO;
import net.xianmu.usercenter.inbound.tenant.vo.TenantPageResultVO;
import net.xianmu.usercenter.starter.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 租户HTTP接口测试
 * 
 * <AUTHOR>
 * @date 2023/12/15
 */
@Slf4j
public class TenantHttpTest extends BaseTest {

    @Autowired
    private TenantController tenantController;

    @Test
    public void testGetTenantsPage() {
        // 创建查询条件
        TenantPageQueryVO queryVO = new TenantPageQueryVO();
        queryVO.setType(0); // 品牌方
        queryVO.setTenantName("测试"); // 租户名称模糊查询
        queryVO.setPageIndex(1);
        queryVO.setPageSize(10);

        log.info("测试分页查询租户列表，查询条件：{}", JSON.toJSONString(queryVO));

        // 调用接口
        CommonResult<PageInfo<TenantPageResultVO>> result = tenantController.getTenantsPage(queryVO);

        // 输出结果
        log.info("查询结果：{}", JSON.toJSONString(result));

        // 验证结果
        if (result != null &&  ResultStatusEnum.OK.getStatus().equals(result.getStatus())) {
            PageInfo<TenantPageResultVO> pageInfo = result.getData();
            log.info("查询成功，总记录数：{}，当前页记录数：{}", pageInfo.getTotal(), pageInfo.getList().size());
            
            // 输出每条记录的详细信息
            for (TenantPageResultVO tenant : pageInfo.getList()) {
                log.info("租户信息：ID={}, 名称={}, 类型={}, 状态={}", 
                    tenant.getTenantId(), tenant.getTenantName(), tenant.getType(), tenant.getStatus());
            }
        } else {
            log.error("查询失败：{}", result != null ? result.getMsg() : "返回结果为空");
        }
    }

    @Test
    public void testGetTenantsPageWithoutCondition() {
        // 创建查询条件（不设置具体条件，只设置分页参数）
        TenantPageQueryVO queryVO = new TenantPageQueryVO();
        queryVO.setPageIndex(1);
        queryVO.setPageSize(5);

        log.info("测试分页查询租户列表（无查询条件），查询条件：{}", JSON.toJSONString(queryVO));

        // 调用接口
        CommonResult<PageInfo<TenantPageResultVO>> result = tenantController.getTenantsPage(queryVO);

        // 输出结果
        log.info("查询结果：{}", JSON.toJSONString(result));

        // 验证结果
        if (result != null &&  ResultStatusEnum.OK.getStatus().equals(result.getStatus())) {
            PageInfo<TenantPageResultVO> pageInfo = result.getData();
            log.info("查询成功，总记录数：{}，当前页记录数：{}", pageInfo.getTotal(), pageInfo.getList().size());
        } else {
            log.error("查询失败：{}", result != null ? result.getMsg() : "返回结果为空");
        }
    }

    @Test
    public void testGetTenantsPageWithInvalidParams() {
        // 创建无效的查询条件
        TenantPageQueryVO queryVO = new TenantPageQueryVO();
        queryVO.setPageIndex(0); // 无效的页码
        queryVO.setPageSize(2000); // 超过最大限制的页面大小

        log.info("测试分页查询租户列表（无效参数），查询条件：{}", JSON.toJSONString(queryVO));

        try {
            // 调用接口
            CommonResult<PageInfo<TenantPageResultVO>> result = tenantController.getTenantsPage(queryVO);
            log.info("查询结果：{}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.info("参数校验异常（预期行为）：{}", e.getMessage());
        }
    }
}
