package net.xianmu.usercenter.domain.merchant.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.redis.support.cache.annotation.XmCache;
import net.xianmu.redis.support.lock.service.XmLockTemplate;
import net.xianmu.redis.support.util.XmRedisUtils;
import net.xianmu.usercenter.common.constants.MerchantDefaultConstant;
import net.xianmu.usercenter.common.constants.NumberConstant;
import net.xianmu.usercenter.common.constants.RedisConstant;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.converter.FastJsonConverter;
import net.xianmu.usercenter.common.enums.MerchantAccountEnums;
import net.xianmu.usercenter.common.enums.PlaceOrderPermissionTimeLimitedEnum;
import net.xianmu.usercenter.common.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.common.input.command.MerchantAddressCommandInput;
import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.common.input.command.MerchantStoreDomainCommandInput;
import net.xianmu.usercenter.domain.tenant.param.command.TenantCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.util.DateUtil;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.common.util.SystemContextHolder;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;
import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAdapterEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreExtRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.XmMerchantAdapterRepository;
import net.xianmu.usercenter.domain.regional.domain.RegionalOrganizationDomainService;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 17:00
 */
@Service
@Slf4j
public class MerchantStoreDomainService {

    @Autowired
    private MerchantStoreQueryRepository queryRepository;
    @Autowired
    private MerchantStoreCommandRepository merchantStoreCommandRepository;
    @Autowired
    private MerchantStoreAccountQueryRepository merchantStoreAccountQueryRepository;
    @Autowired
    private MerchantStoreAccountCommandRepository merchantStoreAccountCommandRepository;
    @Autowired
    private MerchantStoreExtRepository merchantStoreExtRepository;
    @Autowired
    private XmMerchantAdapterRepository xmMerchantAdapterRepository;
    @Autowired
    private MerchantStoreGroupDomainService merchantStoreGroupDomainService;
    @Autowired
    private MerchantAddressDomainService merchantAddressDomainService;
    @Autowired
    private MerchantStoreAccountDomainService merchantStoreAccountDomainService;
    @Autowired
    private RegionalOrganizationDomainService regionalOrganizationDomainService;
    @Autowired
    private RegionalOrganizationQueryRepository regionalOrganizationQueryRepository;
    @Resource
    private XmRedisUtils xmRedisUtils;
    @Resource
    private XmLockTemplate xmLockTemplate;


    public MerchantStoreEntity getMerchantStoreById(Long id) {
        return queryRepository.selectById(id);
    }


    public List<MerchantStoreEntity> getMerchantStoresByIds(List<Long> idList) {
        return queryRepository.selectByIds(idList);
    }

    public List<MerchantStoreEntity> getMerchantStores(MerchantStoreQueryInput req) {
        if (StrUtil.isBlank(req.getPhone()) && StrUtil.isBlank(req.getPhonePrefix())) {
            return queryRepository.selectByCondition(req);
        }

        // 手机号不为空时填充额外的条件
        List<MerchantStoreAccountEntity> entities = merchantStoreAccountQueryRepository.selectByCondition(MerchantStoreAccountQueryInput.builder().phonePrefix(req.getPhonePrefix()).phone(req.getPhone()).deleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode()).build());

        if (CollUtil.isEmpty(entities)) {
            return Collections.emptyList();
        }
        req.addStoreIdList(entities.stream().map(MerchantStoreAccountEntity::getStoreId).collect(Collectors.toList()));
        return queryRepository.selectByCondition(req);
    }


    public void insertDefaultGroup(TenantCommandInput input) {
        // 门店组
        merchantStoreGroupDomainService.insertDefaultGroup(input.getTenantId());
    }


    public MerchantStoreEntity createMerchantStoreInfo(MerchantStoreDomainCommandInput input) {
        log.info("领域层接收到新建门店请求：input:{}", JSON.toJSONString(input));
        // 参数校验
        this.paramValidate(input, true);
        // 门店
        MerchantStoreCommandInput merchantStoreInput = input.getMerchantStore();
        // 默认门店编号
        if (StrUtil.isBlank(merchantStoreInput.getStoreNo())) {
            merchantStoreInput.setStoreNo(this.generateDefaultStoreNoByRedis(merchantStoreInput.getTenantId()));
        }
        MerchantStoreEntity entity = MerchantStoreEntity.buildCreateEntity(merchantStoreInput);
        entity = merchantStoreCommandRepository.createOrUpdate(entity);
        Long storeId = entity.getId();

        // 地址
        this.handleAddress(input.getMerchantAddressList(), storeId);

        // 账户
        this.handleAccount(input.getMerchantStoreAccountList(), storeId);

        // 分组
        merchantStoreGroupDomainService.bindMerchantStoreGroupByDefault(input.getGroupId(), entity.getTenantId(), storeId);
        return entity;
    }


    public MerchantStoreEntity createMerchantStoreInfoBatch(MerchantStoreDomainCommandInput input) {
        log.info("领域层接收到批量新建门店请求：input:{}", JSON.toJSONString(input));
        // 1.参数校验
        ValidateUtil.paramValidate(input, "merchantStore", "merchantAddressList", "merchantStoreAccountList");
        MerchantStoreCommandInput merchantStoreInput = input.getMerchantStore();
        List<MerchantStoreAccountCommandInput> merchantStoreAccountList = input.getMerchantStoreAccountList();
        List<MerchantAddressCommandInput> merchantAddressList = input.getMerchantAddressList();
        this.storeValidate(merchantStoreInput, true);
        this.addressValidate(merchantAddressList);
        this.accountValidate(merchantStoreAccountList, true);
        this.databaseValidate(merchantStoreInput);

        // 2.生成门店信息
        // 默认门店编号
        if (StrUtil.isBlank(merchantStoreInput.getStoreNo())) {
            merchantStoreInput.setStoreNo(this.generateDefaultStoreNoByRedis(merchantStoreInput.getTenantId()));
        }
        MerchantStoreEntity entity = MerchantStoreEntity.buildCreateEntity(merchantStoreInput);
        entity = merchantStoreCommandRepository.createOrUpdate(entity);
        Long storeId = entity.getId();

        // 3.地址
        merchantAddressList.forEach(address -> address.setStoreId(storeId));
        merchantAddressDomainService.createAddressForMerchantBatchCreate(merchantAddressList);

        // 4.账户
        merchantStoreAccountList.forEach(account -> account.setStoreId(storeId));
        merchantStoreAccountDomainService.createAccountForMerchantBatchCreate(merchantStoreAccountList);

        // 分组
        merchantStoreGroupDomainService.bindMerchantStoreGroupByDefault(input.getGroupId(), entity.getTenantId(), storeId);
        return entity;
    }


    /**
     * @param tenantId
     * @param storeId
     * @return
     */
    private String getDefaultStoreNo(Long tenantId, Long storeId) {
        // 校验时剔除当前id
        Long currentId = storeId == null ? -1L : storeId;
        // 注意：这里还是会存在并发的问题,核心问题是生成了StoreNo后，并不能立马写入数据库，导致并发较高的时候会出现问题
        String storeNo = this.generateDefaultStoreNo(tenantId);
        boolean exist = storeNoExist(storeNo, tenantId, currentId);
        if (exist) {
            // 如果默认生成的编号已经被人提前用了，以产品的要求在storeNo前面加上"mendian"
            storeNo = MerchantDefaultConstant.STORE_NO_DEFAULT_PRE + storeNo;
            // 再次校验
            exist = storeNoExist(storeNo, tenantId, currentId);
            if (exist) {
                // 最后兜底,uuid随机一个8位的字符串
                storeNo = StringUtils.createInitPassword();
            }
        }
        return storeNo;
    }

    private synchronized String generateDefaultStoreNo(Long tenantId) {
        // 查看租户维度下门店个数
        Integer storeNum = queryRepository.countStoreNum(tenantId);
        return String.valueOf(storeNum + 1);
    }

    private void handleAddress(List<MerchantAddressCommandInput> list, Long storeId) {
        if (CollUtil.isEmpty(list)) {
            log.warn("门店地址为空。");
        }
        list.forEach(address -> {
            if (null == address.getId()) {
                address.setStoreId(storeId);
                merchantAddressDomainService.create(address);
            } else {
                merchantAddressDomainService.update(address);
            }
        });
    }

    private void handleAccount(List<MerchantStoreAccountCommandInput> list, Long storeId) {
        if (CollUtil.isEmpty(list)) {
            log.warn("账户列表为空。");
        }
        list.forEach(account -> {
            if (null == account.getId()) {
                account.setStoreId(storeId);
                merchantStoreAccountDomainService.createAccount(account);
            } else {
                merchantStoreAccountDomainService.updateAccount(account);
            }
        });
    }


    /****
     * 参数校验
     * @param input isCreate
     */
    private void paramValidate(MerchantStoreDomainCommandInput input, boolean isCreate) {
        ValidateUtil.paramValidate(input);
        MerchantStoreCommandInput merchantStore = input.getMerchantStore();
        List<MerchantStoreAccountCommandInput> merchantStoreAccountList = input.getMerchantStoreAccountList();
        List<MerchantAddressCommandInput> merchantAddressList = input.getMerchantAddressList();


        // 门店
        this.storeValidate(merchantStore, isCreate);

        // 地址
        this.addressValidate(merchantAddressList);

        // 账号信息校验
        this.accountValidate(merchantStoreAccountList, isCreate);

        // 数据库防重校验
        this.databaseValidate(merchantStore);
    }

    private void storeValidate(MerchantStoreCommandInput merchantStore, boolean isCreate) {
        if (null == merchantStore) {
            return;
        }
        if (isCreate || null != merchantStore.getStoreName()) {
            // 门店
            if (!ValidateUtil.isStoreName(merchantStore.getStoreName())) {
                throw new BizException("门店名称不符合条件，请检查是否包含特殊符号以及门店名称长度");
            }
        }
    }

    private void accountValidate(List<MerchantStoreAccountCommandInput> accountList, boolean isCreate) {
        if (CollUtil.isEmpty(accountList)) {
            return;
        }

        long managerNum = accountList.stream().filter(el -> Objects.equals(el.getType(), MerchantAccountEnums.Type.MANAGER.getCode())).count();
        if (isCreate) {
            if (managerNum != NumberConstant.ONE) {
                throw new BizException("店长有且只能有一个");
            }
        } else {
            // 编辑时type字段可能没有传，所以无需做无店长校验
            if (managerNum > NumberConstant.ONE) {
                throw new BizException("店长有且只能有一个");
            }
        }


        if (accountList.size() > NumberConstant.ELEVEN) {
            throw new BizException("最多有一个店长和十个店员");
        }
        long count = accountList.stream().map(MerchantStoreAccountCommandInput::getPhone).distinct().count();
        if (count < accountList.size()) {
            throw new BizException("同一个门店下不能有相同的手机号码");
        }

    }

    private void addressValidate(List<MerchantAddressCommandInput> merchantAddressList) {
        if (CollUtil.isEmpty(merchantAddressList)) {
            return;
        }
        merchantAddressList.forEach(address -> {
            if (StrUtil.isBlank(address.getProvince()) || StrUtil.isBlank(address.getCity()) || StrUtil.isBlank(address.getArea())) {
                throw new BizException("门店地址不符合条件，请检查是否包含特殊符号以及地址长度");
            }
            if (!ValidateUtil.isAddress(address.getAddress())) {
                throw new BizException("门店地址不符合条件，请检查是否包含特殊符号以及地址长度");
            }
        });
    }

    private void databaseValidate(MerchantStoreCommandInput input) {
        // 校验时剔除当前id
        Long currentId = input.getId() == null ? -1L : input.getId();

        // 防重
        if (StrUtil.isNotBlank(input.getStoreName())) {
            MerchantStoreEntity entity = queryRepository.selectByStoreName(input.getTenantId(), input.getStoreName());
            if (Objects.nonNull(entity) && !entity.getId().equals(currentId)) {
                throw new BizException("门店名称重复");
            }
        }

        // 门店编码唯一性校验
        boolean exist = this.storeNoExist(input.getStoreNo(), input.getTenantId(), currentId);
        if (exist) {
            throw new BizException("门店编号已存在");
        }

    }

    private boolean storeNoExist(String storeNo, Long tenantId, Long currentId) {
        if (StrUtil.isNotBlank(storeNo)) {
            final List<MerchantStoreEntity> merchantStoreEntities = queryRepository.selectByCondition(MerchantStoreQueryInput.builder().storeNo(storeNo).tenantId(tenantId).build());
            if (CollUtil.isNotEmpty(merchantStoreEntities)) {
                // 这里分开写主要是方便阅读
                return merchantStoreEntities.stream().anyMatch(entity -> !entity.getId().equals(currentId));
            }
        }
        return false;
    }

    public Boolean updateMerchantStore(MerchantStoreCommandInput input) {
        ValidateUtil.paramValidate(input, "id");
        MerchantStoreEntity entity = queryRepository.selectById(input.getId());
        if (null == entity) {
            throw new BizException("门店不存在!");
        }

        this.databaseValidate(input);
        if(PlaceOrderPermissionTimeLimitedEnum.LONG.getCode ().equals (input.getPlaceOrderPermissionTimeLimited ())){
            input.setPlaceOrderPermissionExpiryTime (LocalDateTime.parse("9999-12-31 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        return merchantStoreCommandRepository.updateSelective(input);
    }


    public Boolean updateMerchantStoreInfo(MerchantStoreDomainCommandInput input) {
        log.info("领域层接收到编辑门店请求：input:{}", JSON.toJSONString(input));
        // 参数校验
        this.paramValidate(input, false);

        // 门店
        MerchantStoreCommandInput merchantStore = input.getMerchantStore();
        this.updateMerchantStore(merchantStore);
        Long storeId = merchantStore.getId();
        merchantStore.setId(storeId);

        // 地址
        this.handleAddress(input.getMerchantAddressList(), storeId);

        // 账户
        this.handleAccount(input.getMerchantStoreAccountList(), storeId);

        // 分组
        merchantStoreGroupDomainService.bindMerchantStoreGroupByDefault(input.getGroupId(), merchantStore.getTenantId(), merchantStore.getId());

        // 审核？
        Integer auditFlag = input.getAuditFlag();
        if (Objects.nonNull(auditFlag)) {
            this.doAudit(merchantStore, auditFlag);
        }
        return true;
    }

    private void doAudit(MerchantStoreCommandInput merchantStore, Integer auditFlag) {
        Long storeId = merchantStore.getId();
        // 门店
        MerchantStoreCommandInput updateStore = MerchantStoreCommandInput.builder()
                .id(storeId)
                .status(auditFlag)
                .auditTime(LocalDateTime.now())
                .auditRemark(merchantStore.getAuditRemark()).build();
        merchantStoreCommandRepository.updateSelective(updateStore);
        // 账户
        MerchantStoreAccountQueryInput queryInput = MerchantStoreAccountQueryInput.builder()
                .storeId(storeId)
                .tenantId(merchantStore.getTenantId())
                .type(MerchantAccountEnums.Type.MANAGER.getCode()).build();
        List<MerchantStoreAccountEntity> accountEntities = merchantStoreAccountQueryRepository.selectByCondition(queryInput);
        MerchantStoreAccountEntity accountEntity = accountEntities.get(0);
        accountEntity.setAuditTime(LocalDateTime.now());
        accountEntity.setStatus(auditFlag);
        merchantStoreAccountCommandRepository.updateSelective(accountEntity);
    }


    public MerchantStoreEntity createMerchantForBinLog(MerchantStoreCommandInput merchantStoreInput, Long mId) {
        log.info("接收到binlog创建门店请求：input:{}", FastJsonConverter.convert(merchantStoreInput));
        if (null == merchantStoreInput) {
            return null;
        }

        // 1.门店
        merchantStoreInput.setStoreNo(null);
        MerchantStoreEntity entity = MerchantStoreEntity.buildCreateEntityForBinLog(merchantStoreInput);
        entity = merchantStoreCommandRepository.createOrUpdate(entity);
        Long storeId = entity.getId();

        // 2.拓展表
        MerchantStoreExtEntity merchantStoreExtEntity = MerchantStoreExtEntity.buildCreateEntityForBinLog(merchantStoreInput);
        merchantStoreExtEntity.setStoreId(storeId);
        merchantStoreExtRepository.createOrUpdate(merchantStoreExtEntity);

        // 3.鲜沐业务适配表
        XmMerchantAdapterEntity xmMerchantAdapterEntity = XmMerchantAdapterEntity.buildCreateEntityForBinLog(merchantStoreInput);
        xmMerchantAdapterEntity.setStoreId(storeId);
        xmMerchantAdapterRepository.createOrUpdate(xmMerchantAdapterEntity);
        // 3.分组
        //merchantStoreGroupDomainService.bindMerchantStoreGroupByDefault(null, entity.getTenantId(), storeId);

        return entity;
    }


    public void updateMerchantForBinLog(Map<String, String> data, Map<String, String> old, Long storeId) {
        log.info("接收到binlog修改门店请求：storeId:{}, data:{}, old :{}", storeId, FastJsonConverter.convert(data), FastJsonConverter.convert(old));
        // 处理大客户变更
        Long regionalId = this.handleAdminChange(data, old);

        MerchantStoreEntity input = MerchantStoreEntity.builder()
                .id(storeId)
                .regionalId(regionalId)
                .storeName(data.get("mname"))
                .type(MerchantStoreEntity.transTypeFromXm(data.get("enterprise_scale")))
                .status(MerchantStoreEntity.transStatusFromXm(data.get("islock")))
                .registerTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("register_time"), null))
                .remark(null)
                .auditTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("audit_time"), null))
                .auditRemark(data.get("remark"))
                .channelCode(data.get("channel_code"))
                .businessType(data.get("type"))
                .areaNo(Integer.valueOf(data.get("area_no")))
                .createTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("register_time"), null))
                .updateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("update_time"), null))
                .build();
        merchantStoreCommandRepository.updateSelective(input);
    }


    /**
     * 处理大客户变更
     * // 如果发生大客户变更：需要调整MerchantStore的区域映射
     * // 1.单店转大客户：
     * // 2.从大客户A转到大客户B
     */
    private Long handleAdminChange(Map<String, String> data, Map<String, String> old) {
        if (ValidateUtil.isContains(old, "admin_id")) {
            RegionalOrganizationEntity entity;
            // 获取目标大客户所对应的区域
            String adminIdStr = data.get("admin_id");
            if (StrUtil.isBlank(adminIdStr)) {
                // adminIdStr为空代表调整为单店
                // 初始化一个单店类型的区域组织
                entity = RegionalOrganizationEntity.builder()
                        .phone(data.get("phone"))
                        .organizationName(data.get("mname"))
                        .source(RegionalOrganizationEnums.Source.XIANMU.getCode())
                        .size(RegionalOrganizationEnums.Size.MERCHANT.getCode())
                        .status(RegionalOrganizationEnums.Status.NORMAL.getCode())
                        .tenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID)
                        .createTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("create_time"), LocalDateTime.now()))
                        .updateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("update_time"), LocalDateTime.now()))
                        .build();
                entity = regionalOrganizationDomainService.createForBinLog(entity);
            } else {
                entity = regionalOrganizationQueryRepository.selectByAdminAndTenant(Long.valueOf(adminIdStr), TenantDefaultConstant.XIAN_MU_TENANT_ID);
                // 这个时候entity为空代表创建大客户的binlog还没消费，需要手动处理或者重试
                if (null == entity) {
                    log.error("创建大客户的binlog还没消费，需要手动处理.m_id:{}", data.get("m_id"));
                    throw new BizException("创建大客户的binlog还没消费，需要手动处理");
                }

                // 单店转大客户清理掉单店无用的区域组织
            }

            return entity.getId();
        }
        return null;
    }


    /**
     * 基于redis自增生成门店编号
     *
     * @param tenantId
     * @return
     */
    private String generateDefaultStoreNoByRedis(Long tenantId) {
        String incrementKey = RedisConstant.SYSTEM_PREFIX + RedisConstant.MERCHANT_STORE_NO_INCR + tenantId;
        String lockKey = RedisConstant.SYSTEM_PREFIX + RedisConstant.MERCHANT_STORE_NO_LOCK + tenantId;
        Object lastIncrStoreNo = xmRedisUtils.get(incrementKey);
        if (lastIncrStoreNo == null) {
            // 阻塞式锁
            xmLockTemplate.executeLock(lock -> {
                Object o = xmRedisUtils.get(incrementKey);
                if (o == null) {
                    // 暂未设置过期时间
                    Integer currentNo = queryRepository.countStoreNum(tenantId);
                    return xmRedisUtils.incr(incrementKey, currentNo);
                }
                return null;
            }, lockKey);
        }

        Long nextStoreNo = xmRedisUtils.incr(incrementKey, 1);
        String nextStoreNoStr = String.valueOf(nextStoreNo);
        // 校验是否和人工输入的重复
        boolean exist = storeNoExist(nextStoreNoStr, tenantId, -1L);
        if (exist) {
            // 最后兜底,uuid随机一个8位的字符串
            log.warn("自动生成的编号：[{}]已被人为占用，使用随机算法生成一个.", nextStoreNoStr);
            nextStoreNoStr = StringUtils.createInitPassword();
        }
        return nextStoreNoStr;
    }

    public void deleteForBinLog(Long id) {
        merchantStoreCommandRepository.delete(id);
    }
}
