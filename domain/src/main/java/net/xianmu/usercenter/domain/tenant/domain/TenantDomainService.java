package net.xianmu.usercenter.domain.tenant.domain;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import net.xianmu.i18n.exception.I18nBizException;
import net.xianmu.usercenter.common.enums.TenantTypeEnum;
import net.xianmu.usercenter.domain.tenant.param.command.TenantAccountCommandInput;
import net.xianmu.usercenter.domain.tenant.param.command.TenantCommandInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.common.enums.SortTypeEnum;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;
import net.xianmu.usercenter.domain.tenant.repository.MerchantCommandRepository;
import net.xianmu.usercenter.domain.tenant.repository.TenantCommandRepository;
import net.xianmu.usercenter.domain.tenant.repository.TenantQueryRepository;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;
import net.xianmu.usercenter.facade.auth.AuthUserFacade;
import net.xianmu.usercenter.facade.auth.input.RoleAdminInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Service
public class TenantDomainService {

    @Autowired
    private TenantAccountDomainService tenantAccountDomainService;

    @Autowired
    private TenantQueryRepository tenantQueryRepository;

    @Autowired
    private TenantCommandRepository tenantCommandRepository;

    @Autowired
    private MerchantCommandRepository merchantCommandRepository;

    @Autowired
    private AuthUserFacade authUserFacade;

    public TenantEntity getTenantById(Long id) {
        return tenantQueryRepository.getTenantById(id);
    }


    public List<TenantEntity> getTenantsByIds(List<Long> idList) {
        return tenantQueryRepository.getTenantsByIds(idList);
    }


    public PageInfo<TenantAndBusinessEntity> getTenantsPage(TenantQueryInput req, PageQueryInput pageQueryInput) {
        // 参数处理
        if (StrUtil.isBlank(req.getSortWord())) {
            req.setSortWord("id");
        }
        if (StrUtil.isBlank(req.getSortType())) {
            req.setSortType(SortTypeEnum.DESCENDING.getKeyWord());
        } else {
            req.setSortType(req.getSortType().equals(SortTypeEnum.ASCENDING.getType()) ? SortTypeEnum.ASCENDING.getKeyWord() : SortTypeEnum.DESCENDING.getKeyWord());
        }

        return tenantQueryRepository.getTenantsPage(req, pageQueryInput);
    }

    public List<TenantEntity> getTenants(TenantQueryInput req) {
        return tenantQueryRepository.getTenants(req);
    }


    public List<TenantAndBusinessEntity> getTenantAndCompanyList(TenantQueryInput req) {
        return tenantQueryRepository.getTenantAndCompanyList(req);
    }


    public List<TenantAndBusinessEntity> getTenantAndCompanyByIds(List<Long> idList) {
        return tenantQueryRepository.getTenantAndCompanyByIds(idList);
    }






    public Long insertTenantMainInfo(TenantCommandInput input) {
        this.checkValid(input);

        // 租户
        TenantEntity tenantEntity = TenantEntity.buildCreateEntity(input);
        tenantEntity = tenantCommandRepository.createOrUpdate(tenantEntity);
        input.setTenantId(tenantEntity.getId());

        // 创建超级管理员角色
        final Long roleId = authUserFacade.addNewAdminRole(RoleAdminInput.buildDefaultRoleAdminInput(input.getTenantId(), input.getOpAuthUserId(), input.getSystemOrigin()));

        // 账户
        TenantAccountCommandInput accountCommandInput = TenantCommandInput.converterToTenantAccountCommandInput(input);
        accountCommandInput.setRoleIds(Collections.singletonList(roleId));
        tenantAccountDomainService.create(accountCommandInput);

        // 品牌
        MerchantEntity merchantEntity = MerchantEntity.buildByTenantIdAndName(input.getTenantId(), input.getMerchantName());
        merchantCommandRepository.createOrUpdate(merchantEntity);
        return tenantEntity.getId();
    }


    private void checkValid(TenantCommandInput input) {
        // admin_id 不可重复
        List<TenantEntity> tenants = tenantQueryRepository.getTenantsNonFuzzy(TenantQueryInput.builder().adminId(input.getAdminId()).build());
        if (!CollectionUtils.isEmpty(tenants)) {
            throw new BizException("鲜沐大客户Id 不可重复");
        }

        // 商城名称不可重复
        List<TenantEntity> merchants = tenantQueryRepository.getTenantsNonFuzzy(TenantQueryInput.builder().tenantName(input.getMerchantName()).build());
        if (!CollectionUtils.isEmpty(merchants)){
            String errorMsg = TenantTypeEnum.OUTER_ORDER.getCode().equals(input.getType()) ? "公司简称" : "商城名称";
            throw new I18nBizException("{0}不可重复", errorMsg);
        }
    }


    public void update(TenantCommandInput input) {
        // 参数校验
        ValidateUtil.paramValidate(input, "tenantId");
        TenantEntity entity = tenantQueryRepository.getTenantById(input.getTenantId());
        if(null == entity) {
            throw new BizException("租户信息不存在");
        }

        entity.setProfitSharingSwitch(input.getProfitSharingSwitch());
        entity.setOnlinePayChannel(input.getOnlinePayChannel());
        tenantCommandRepository.createOrUpdate(entity);
    }


    public void batchUpdateOp(List<Long> ids, String opUname, Long opUid) {
        tenantCommandRepository.batchUpdateOp(ids, opUname, opUid);
    }

}
