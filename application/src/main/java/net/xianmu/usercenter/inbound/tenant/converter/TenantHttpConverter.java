package net.xianmu.usercenter.inbound.tenant.converter;

import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import net.xianmu.usercenter.inbound.tenant.vo.TenantPageQueryVO;
import net.xianmu.usercenter.inbound.tenant.vo.TenantPageResultVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 租户HTTP接口转换器
 * 
 * <AUTHOR>
 * @date 2023/12/15
 */
public class TenantHttpConverter {

    private TenantHttpConverter() {
        // 无需实现
    }

    /**
     * 将HTTP请求VO转换为TenantQueryInput
     * 
     * @param vo HTTP请求VO
     * @return TenantQueryInput
     */
    public static TenantQueryInput toTenantQueryInput(TenantPageQueryVO vo) {
        if (vo == null) {
            return null;
        }
        
        TenantQueryInput input = new TenantQueryInput();
        input.setType(vo.getType());
        input.setTenantName(vo.getTenantName());
        
        return input;
    }

    /**
     * 将HTTP请求VO转换为PageQueryInput
     * 
     * @param vo HTTP请求VO
     * @return PageQueryInput
     */
    public static PageQueryInput toPageQueryInput(TenantPageQueryVO vo) {
        if (vo == null) {
            return null;
        }
        
        PageQueryInput input = new PageQueryInput();
        input.setPageIndex(vo.getPageIndex() != null ? vo.getPageIndex() : 1);
        input.setPageSize(vo.getPageSize() != null ? vo.getPageSize() : 10);
        
        return input;
    }

    /**
     * 将TenantAndBusinessEntity转换为TenantPageResultVO
     *
     * @param entity 实体对象
     * @return TenantPageResultVO
     */
    public static TenantPageResultVO toTenantPageResultVO(TenantAndBusinessEntity entity) {
        if (entity == null) {
            return null;
        }

        TenantPageResultVO vo = new TenantPageResultVO();
        vo.setTenantId(entity.getTenantId());
        vo.setTenantName(entity.getTenantName());
        vo.setPhone(entity.getPhone());
        vo.setType(entity.getTenantType()); // 注意：实体中是tenantType字段
        vo.setStatus(entity.getStatus());
        vo.setCompanyName(entity.getCompanyName());
        vo.setContactName(entity.getContactName());
        vo.setCreditCode(entity.getCreditCode());
        vo.setAdminId(entity.getAdminId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setOpUname(entity.getOpUname());

        return vo;
    }

    /**
     * 将TenantAndBusinessEntity列表转换为TenantPageResultVO列表
     * 
     * @param entityList 实体对象列表
     * @return TenantPageResultVO列表
     */
    public static List<TenantPageResultVO> toTenantPageResultVOList(List<TenantAndBusinessEntity> entityList) {
        if (entityList == null) {
            return Collections.emptyList();
        }
        
        List<TenantPageResultVO> voList = new ArrayList<>();
        for (TenantAndBusinessEntity entity : entityList) {
            voList.add(toTenantPageResultVO(entity));
        }
        
        return voList;
    }
}
