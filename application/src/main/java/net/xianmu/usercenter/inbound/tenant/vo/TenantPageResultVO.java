package net.xianmu.usercenter.inbound.tenant.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 租户分页查询结果VO
 * 
 * <AUTHOR>
 * @date 2023/12/15
 */
@Data
public class TenantPageResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 管理员手机号
     */
    private String phone;

    /**
     * 租户类型：0-供应商,1-品牌方,2-帆台
     */
    private Integer type;

    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;

    /**
     * 工商主体
     */
    private String companyName;

    /**
     * 租户联系人名称
     */
    private String contactName;

    /**
     * 信用代码/税号
     */
    private String creditCode;

    /**
     * 大客户Id
     */
    private Long adminId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private String opUname;
}
