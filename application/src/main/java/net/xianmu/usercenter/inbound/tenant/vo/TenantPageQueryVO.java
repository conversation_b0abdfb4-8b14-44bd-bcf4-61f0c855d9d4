package net.xianmu.usercenter.inbound.tenant.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 租户分页查询请求VO
 * 
 * <AUTHOR>
 * @date 2023/12/15
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class TenantPageQueryVO extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *租户类型：0-品牌方,1-鲜沐,2-帆台,3-外单
     */
    private Integer type;

    /**
     * 租户名称（支持模糊匹配）
     */
    @Size(max = 100, message = "租户名称长度不能超过100个字符")
    private String tenantName;
}
