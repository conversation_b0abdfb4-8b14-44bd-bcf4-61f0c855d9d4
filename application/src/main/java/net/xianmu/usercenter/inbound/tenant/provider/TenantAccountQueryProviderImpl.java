package net.xianmu.usercenter.inbound.tenant.provider;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.i18n.result.util.I18nDubboResponseUtil;
import net.xianmu.usercenter.api.tenant.dto.TenantAccountDTO;
import net.xianmu.usercenter.api.tenant.service.TenantAccountQueryService;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.tenant.provider.TenantAccountQueryProvider;
import net.xianmu.usercenter.client.tenant.req.TenantAccountListQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.inbound.page.PageConverter;
import net.xianmu.usercenter.inbound.tenant.converter.TenantAccountConverter;
import net.xianmu.usercenter.inbound.tenant.converter.TenantAccountReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 10:34
 */
@Slf4j
@DubboService
public class TenantAccountQueryProviderImpl implements TenantAccountQueryProvider {

    @Autowired
    private TenantAccountQueryService queryService;

    @Override
    public DubboResponse<TenantAccountResultResp> getTenantAccountVO(Long authUserId) {
        return DubboResponse.getOK(TenantAccountConverter.toTenantAccountResultResp(queryService.getTenantAccount(authUserId)));
    }

    @Override
    public DubboResponse<List<TenantAccountResultResp>> getTenantAccountsByAuthUserIds(List<Long> list) {
        return DubboResponse.getOK(TenantAccountConverter.toTenantAccountResultRespList(queryService.getTenantAccountsByAuthUserIds(list)));
    }

    @Override
    public DubboResponse<TenantAccountResultResp> getTenantAccountById(Long id) {
        return DubboResponse.getOK(TenantAccountConverter.toTenantAccountResultResp(queryService.getTenantAccountById(id)));
    }

    @Override
    @Deprecated
    public DubboResponse<List<TenantAccountResultResp>> getTenantAccounts(TenantAccountQueryReq req) {
        return DubboResponse.getOK(TenantAccountConverter.toTenantAccountResultRespList(queryService.getTenantAccounts(TenantAccountReqConverter.toTenantAccountListQueryInput(req))));
    }

    @Override
    public DubboResponse<List<TenantAccountResultResp>> getTenantAccountsByKeys(TenantAccountQueryReq req) {
        // 空参数直接返回空集合
        if (req == null || allQueryFieldsEmpty(req)) {
            log.warn("查询缺失必要参数！");
            return I18nDubboResponseUtil.getDefaultError("查询缺失必要参数");
        }
        return DubboResponse.getOK(TenantAccountConverter.toTenantAccountResultRespList(queryService.getTenantAccounts(TenantAccountReqConverter.toTenantAccountListQueryInput(req))));
    }

    // 校验所有查询条件字段是否为空
    private boolean allQueryFieldsEmpty(TenantAccountQueryReq req) {
        return CollUtil.isEmpty(req.getIdList()) &&
                req.getTenantId() == null &&
                StringUtils.isEmpty(req.getPhone()) &&
                StringUtils.isEmpty(req.getEmail()) &&
                StringUtils.isEmpty(req.getNickName());
    }

    @Override
    public DubboResponse<List<TenantAccountResultResp>> getTenantAccountByTenantIdsAndPhone(List<Long> tenantIdList, String phone) {
        return DubboResponse.getOK(TenantAccountConverter.toTenantAccountResultRespList(queryService.getTenantAccountByTenantIdsAndPhone(tenantIdList, phone)));
    }

    @Override
    public DubboResponse<PageInfo<TenantAccountResultResp>> getTenantAccountsPage(TenantAccountListQueryReq tenantAccountListQueryReq, PageQueryReq pageQueryReq) {

        PageInfo<TenantAccountDTO> page = queryService.getTenantAccountsPage(TenantAccountReqConverter.toTenantAccountListQueryInput(tenantAccountListQueryReq), PageConverter.toPageQueryInput(pageQueryReq));
        PageInfo<TenantAccountResultResp> pageResp = PageInfoConverter.toPageResp(page, TenantAccountConverter::toTenantAccountResultResp);
        return DubboResponse.getOK(pageResp);
    }
}
